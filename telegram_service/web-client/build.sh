#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "Error: pnpm is not installed. Please install pnpm first."
    echo "You can install it with: npm install -g pnpm"
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pnpm install

# Build the web client
echo "Building Jay's Blog..."
pnpm build

echo "Build complete!"
echo ""
echo "To start the development server, run:"
echo "pnpm dev"
echo ""
echo "To preview the production build, run:"
echo "pnpm preview" 