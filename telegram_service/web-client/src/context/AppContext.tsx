import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Config, Message } from '@/types';
import api from '@/services/api';
import { toast } from 'react-toastify';

interface AppContextType {
  config: Config;
  setConfig: (config: Config) => void;
  messages: Message[];
  sendMessage: (content: string) => Promise<void>;
  sendEmoji: (emojiName: string) => Promise<void>;
  fetchHistory: (count: number) => Promise<void>;
  searchMessages: (keyword: string) => Promise<void>;
  isConnected: boolean;
  isPolling: boolean;
  startPolling: () => void;
  stopPolling: () => void;
  clearMessages: () => void;
  resendMessage: (message: Message) => Promise<void>;
}

const defaultConfig: Config = {
  serviceURL: localStorage.getItem('serviceURL') || 'http://45.78.48.108:8207',
  apiKey: localStorage.getItem('apiKey') || 'nolunch',
  pollInterval: Number(localStorage.getItem('pollInterval')) || 1,
  lastTimestamp: 0,
  username: localStorage.getItem('username') || 'You',
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [config, setConfig] = useState<Config>(defaultConfig);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPolling, setIsPolling] = useState<boolean>(false);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Save config to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('serviceURL', config.serviceURL);
    localStorage.setItem('apiKey', config.apiKey);
    localStorage.setItem('pollInterval', config.pollInterval.toString());
    localStorage.setItem('username', config.username);
  }, [config]);

  // Start polling if apiKey is available
  useEffect(() => {
    if (config.apiKey) {
      startPolling();
    }
    return () => {
      stopPolling();
    };
  }, [config.apiKey, config.pollInterval]);

  const startPolling = () => {
    if (isPolling || !config.apiKey) return;
    
    setIsPolling(true);
    
    // Initial connection check
    checkConnection();
    
    const interval = setInterval(() => {
      pollMessages();
    }, config.pollInterval * 1000);
    
    setPollingInterval(interval);
  };

  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
    setIsPolling(false);
  };

  const checkConnection = async () => {
    try {
      const response = await api.getMessages(config);
      setIsConnected(response.success);
      if (!response.success) {
        toast.error('Failed to connect to the server');
      }
    } catch (error) {
      setIsConnected(false);
      toast.error('Connection error');
    }
  };

  const pollMessages = async () => {
    try {
      const response = await api.getMessages(config);
      
      if (response.success && response.messages.length > 0) {
        // Sort messages by timestamp
        const sortedMessages = [...response.messages].sort((a, b) => {
          const tsA = typeof a.timestamp === 'string' ? parseInt(a.timestamp) : a.timestamp;
          const tsB = typeof b.timestamp === 'string' ? parseInt(b.timestamp) : b.timestamp;
          return tsA - tsB;
        });

        // Update messages and lastTimestamp
        setMessages(prevMessages => {
          const newMessages = [...prevMessages];
          
          sortedMessages.forEach(msg => {
            // Check if message already exists
            const exists = newMessages.some(
              existingMsg => 
                existingMsg.timestamp === msg.timestamp && 
                existingMsg.header === msg.header && 
                (existingMsg.content === msg.content || 
                 existingMsg.message?.Content === msg.message?.Content)
            );
            
            if (!exists) {
              newMessages.push(msg);
            }
          });
          
          return newMessages;
        });

        // Update lastTimestamp
        const lastMsg = sortedMessages[sortedMessages.length - 1];
        const lastTs = typeof lastMsg.timestamp === 'string' 
          ? parseInt(lastMsg.timestamp) 
          : lastMsg.timestamp;
          
        if (lastTs > config.lastTimestamp) {
          setConfig({
            ...config,
            lastTimestamp: lastTs
          });
        }
      }
    } catch (error) {
      console.error('Error polling messages:', error);
    }
  };

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;
    
    const timestamp = Math.floor(Date.now() / 1000);
    const messageId = Date.now().toString();
    const command = {
      id: messageId,
      type: 'pbb',
      data: { content },
      timestamp,
    };

    // Add message to local state immediately
    const newMessage: Message = {
      id: messageId,
      header: config.username,
      content,
      timestamp,
      isCurrentUser: true,
      msg_type: 1,
      sendStatus: 'pending',
    };
    
    setMessages(prev => [...prev, newMessage]);

    // Send to server
    try {
      const response = await api.sendCommand(config, command);
      if (response.success) {
        // Update message status to sent
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId ? { ...msg, sendStatus: 'sent' } : msg
          )
        );
      } else {
        // Update message status to failed
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId ? { ...msg, sendStatus: 'failed' } : msg
          )
        );
        toast.error(`Failed to send message: ${response.error}`);
      }
    } catch (error) {
      // Update message status to failed
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? { ...msg, sendStatus: 'failed' } : msg
        )
      );
      toast.error('Error sending message');
    }
  };

  const sendEmoji = async (emojiName: string) => {
    const timestamp = Math.floor(Date.now() / 1000);
    const messageId = Date.now().toString();
    const command = {
      id: messageId,
      type: 'emoji',
      data: { name: emojiName },
      timestamp,
    };

    // Add emoji to local state immediately
    const newMessage: Message = {
      id: messageId,
      header: config.username,
      content: `Emoji: ${emojiName}`,
      timestamp,
      isCurrentUser: true,
      msg_type: 47,
      sendStatus: 'pending',
    };
    
    setMessages(prev => [...prev, newMessage]);

    // Send to server
    try {
      const response = await api.sendCommand(config, command);
      if (response.success) {
        // Update message status to sent
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId ? { ...msg, sendStatus: 'sent' } : msg
          )
        );
      } else {
        // Update message status to failed
        setMessages(prev => 
          prev.map(msg => 
            msg.id === messageId ? { ...msg, sendStatus: 'failed' } : msg
          )
        );
        toast.error(`Failed to send emoji: ${response.error}`);
      }
    } catch (error) {
      // Update message status to failed
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? { ...msg, sendStatus: 'failed' } : msg
        )
      );
      toast.error('Error sending emoji');
    }
  };

  // Add resend message functionality
  const resendMessage = async (message: Message) => {
    if (!message.id || !message.content) return;
    
    // Update message status back to pending
    setMessages(prev => 
      prev.map(msg => 
        msg.id === message.id ? { ...msg, sendStatus: 'pending' } : msg
      )
    );

    // Create command based on message type
    const timestamp = Math.floor(Date.now() / 1000);
    const isEmoji = message.msg_type === 47;
    const command = {
      id: message.id,
      type: isEmoji ? 'emoji' : 'pbb',
      data: isEmoji 
        ? { name: message.content.replace('Emoji: ', '') } 
        : { content: message.content },
      timestamp,
    };

    // Send to server
    try {
      const response = await api.sendCommand(config, command);
      if (response.success) {
        // Update message status to sent
        setMessages(prev => 
          prev.map(msg => 
            msg.id === message.id ? { ...msg, sendStatus: 'sent' } : msg
          )
        );
      } else {
        // Keep as failed
        setMessages(prev => 
          prev.map(msg => 
            msg.id === message.id ? { ...msg, sendStatus: 'failed' } : msg
          )
        );
        toast.error(`Failed to resend: ${response.error}`);
      }
    } catch (error) {
      // Keep as failed
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id ? { ...msg, sendStatus: 'failed' } : msg
        )
      );
      toast.error('Error resending message');
    }
  };

  const fetchHistory = async (count: number) => {
    const command = {
      id: Date.now().toString(),
      type: 'history',
      data: { count },
      timestamp: Math.floor(Date.now() / 1000),
    };

    toast.info(`Fetching last ${count} messages...`);

    try {
      const response = await api.sendCommand(config, command);
      if (!response.success) {
        toast.error(`Failed to fetch history: ${response.error}`);
      }
    } catch (error) {
      toast.error('Error fetching history');
    }
  };

  const searchMessages = async (keyword: string) => {
    if (!keyword.trim()) return;
    
    const command = {
      id: Date.now().toString(),
      type: 'search',
      data: { keyword },
      timestamp: Math.floor(Date.now() / 1000),
    };

    toast.info(`Searching for "${keyword}"...`);

    try {
      const response = await api.sendCommand(config, command);
      if (!response.success) {
        toast.error(`Failed to search messages: ${response.error}`);
      }
    } catch (error) {
      toast.error('Error searching messages');
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <AppContext.Provider
      value={{
        config,
        setConfig,
        messages,
        sendMessage,
        sendEmoji,
        fetchHistory,
        searchMessages,
        isConnected,
        isPolling,
        startPolling,
        stopPolling,
        clearMessages,
        resendMessage,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}; 