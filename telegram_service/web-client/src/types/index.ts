export interface Config {
  serviceURL: string;
  apiKey: string;
  pollInterval: number;
  lastTimestamp: number;
  username: string;
}

export interface Command {
  id: string;
  type: string;
  data: Record<string, any>;
  timestamp: number;
}

export interface Message {
  id?: string;
  header?: string;
  msg_type?: number;
  message?: {
    Content?: string;
    ImageDataURI?: string;
  };
  content?: string;
  timestamp: string | number;
  isCurrentUser?: boolean;
  sendStatus?: 'pending' | 'sent' | 'failed';
}

export interface ApiResponse<T> {
  success: boolean;
  error?: string;
  data?: T;
}

export interface MessagesResponse {
  success: boolean;
  messages: Message[];
} 