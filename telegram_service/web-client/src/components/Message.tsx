import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { format } from 'date-fns';
import { Message as MessageType } from '@/types';
import { FiImage, FiRefreshCw } from 'react-icons/fi';
import { useApp } from '@/context/AppContext';

interface MessageContainerProps {
  isCurrentUser: boolean;
}

const MessageContainer = styled.div<MessageContainerProps>`
  display: flex;
  flex-direction: column;
  align-self: ${props => props.isCurrentUser ? 'flex-end' : 'flex-start'};
  max-width: 70%;
  margin-bottom: 16px;
`;

interface MessageBubbleProps {
  isCurrentUser: boolean;
  status?: string;
}

const MessageBubble = styled.div<MessageBubbleProps>`
  background-color: ${props => {
    if (props.status === 'failed') return 'var(--error-light)';
    return props.isCurrentUser ? 'var(--message-sent)' : 'var(--message-received)';
  }};
  border-radius: 12px;
  padding: 10px 16px;
  box-shadow: var(--shadow);
  border: 1px solid ${props => props.status === 'failed' ? 'var(--error)' : 'var(--border-color)'};
  position: relative;
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
`;

const MessageSender = styled.span<MessageContainerProps>`
  font-weight: bold;
  color: ${props => props.isCurrentUser ? 'var(--accent-color)' : 'var(--primary-color)'};
`;

const MessageTime = styled.span`
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-left: 8px;
`;

const MessageContent = styled.div`
  word-break: break-word;
  white-space: pre-wrap;
`;

const MessageEmoji = styled.div`
  font-size: 1.5rem;
  padding: 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const EmojiName = styled.span`
  font-size: 1rem;
  color: var(--text-secondary);
`;

const ImagePlaceholder = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--accent-color);
  font-style: italic;
`;

const EmojiImage = styled.img`
  max-width: 120px;
  max-height: 120px;
  border-radius: 4px;
`;

// Add new styled component for chat message images
const ChatMessageImage = styled.img`
  max-width: 250px; // Adjust size as needed
  max-height: 250px; // Adjust size as needed
  border-radius: 8px;
  margin-top: 8px;
  cursor: pointer; // Indicates it's clickable
`;

// Styled components for the image modal
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; // Ensure it's on top
  cursor: pointer;
`;

const ModalContent = styled.img`
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  cursor: default; // Keep default cursor for the image itself
`;

const EmojiContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ResendButton = styled.button`
  position: absolute;
  bottom: -10px;
  right: 10px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    background-color: var(--primary-color);
  }
`;

const StatusIndicator = styled.div<{ status?: string }>`
  font-size: 0.7rem;
  margin-top: 2px;
  text-align: right;
  font-style: italic;
  color: ${props => {
    if (props.status === 'failed') return 'var(--error)';
    if (props.status === 'pending') return 'var(--warning)';
    return 'var(--success)';
  }};
`;

interface MessageProps {
  message: MessageType;
}

// Helper function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  const entities: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&#x2F;': '/',
    '&#x3D;': '=',
  };
  
  return text.replace(/&amp;|&lt;|&gt;|&quot;|&#39;|&#x2F;|&#x3D;/g, match => entities[match]);
};

const Message: React.FC<MessageProps> = ({ message }) => {
  const { resendMessage } = useApp();
  const isCurrentUser = !!message.isCurrentUser;
  const [emojiUrl, setEmojiUrl] = useState<string | null>(null);
  const [emojiDesc, setEmojiDesc] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [displayableImageSrc, setDisplayableImageSrc] = useState<string | null>(null);
  const [isImageLoading, setIsImageLoading] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalImageSrc, setModalImageSrc] = useState<string | null>(null);
  
  // Format timestamp
  const timestamp = typeof message.timestamp === 'string' 
    ? parseInt(message.timestamp) 
    : message.timestamp;
  
  const formattedTime = format(new Date(timestamp * 1000), 'HH:mm:ss');
  
  // Get message content
  let content = '';
  const messageContent = message.message?.Content;
  if (messageContent) {
    content = messageContent;
  } else if (message.content) {
    content = message.content;
  } 
  
  // Get sender name
  const sender = message.header || 'Unknown';
  
  // Determine message type
  const msgType = message.msg_type || 1; // Default to text message
  const isEmoji = msgType === 47 || content.includes('<emoji') || content.startsWith('Emoji:');
  const isImage = msgType === 3;

  // Extract emoji information or decode image content
  useEffect(() => {
    // Reset image state on message change
    setDisplayableImageSrc(null);
    setIsImageLoading(false);
    setImageError(false);
    console.log('message', message, content)
    console.log("messageContent", message.message?.Content)
    console.log("content", content)
    if (isEmoji) {
      // Decode HTML entities in content
      const decodedContent = decodeHtmlEntities(content);
      // log content
 
      // Try to extract CDN URL from the content if it's an XML-like format
      const cdnUrlMatch = decodedContent.match(/cdnurl="([^"]+)"/);
      if (cdnUrlMatch && cdnUrlMatch[1]) {
        setEmojiUrl(cdnUrlMatch[1]);
      }
      
      // Try to extract description
      const descMatch = decodedContent.match(/desc="([^"]*)"/);
      if (descMatch) {
        setEmojiDesc(descMatch[1] || "Emoji");
      } else if (decodedContent.startsWith('Emoji:')) {
        setEmojiDesc(decodedContent.substring(6).trim());
      } else {
        setEmojiDesc("Emoji");
      }
    } else if (isImage) {
      // 1. Check for pre-formatted Data URI from backend
      if (message.message?.ImageDataURI) {
        setDisplayableImageSrc(message.message.ImageDataURI);
      } 
      // 2. Check if Content looks like Base64 and needs decoding
      else if (messageContent && messageContent.startsWith('/9j/')) { // Basic check for JPEG Base64
        setIsImageLoading(true);
        try {
          // Construct the Data URI directly
          // Note: Assumes JPEG. More robust checks might be needed for other types.
          const dataUri = `data:image/jpeg;base64,${messageContent}`;
          setDisplayableImageSrc(dataUri);
        } catch (error) {
          console.error("Error constructing image data URI:", error);
          setImageError(true);
        } finally {
          setIsImageLoading(false);
        }
      } else {
        // Content is not a recognized format or missing
        setImageError(true); 
      }
    }
  }, [message, isEmoji, isImage, content, messageContent]);

  const handleImageError = () => {
    // This handles errors for images loaded via src (like emoji CDN or if data URI is malformed)
    setImageError(true);
    setDisplayableImageSrc(null); // Clear the src if loading failed
    setIsImageLoading(false);
  };

  // Format content for display
  const displayContent = React.useMemo(() => {
    if (!isEmoji && !isImage) {
      return decodeHtmlEntities(content);
    }
    return content;
  }, [content, isEmoji, isImage]);

  const handleResend = () => {
    if (message.sendStatus === 'failed') {
      resendMessage(message);
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (message.sendStatus) {
      case 'pending': return 'Sending...';
      case 'failed': return 'Failed to send';
      case 'sent': return '';
      default: return '';
    }
  };

  // Function to open the image modal
  const handleImageClick = (src: string) => {
    setModalImageSrc(src);
    setIsModalOpen(true);
  };

  // Function to close the image modal
  const closeModal = () => {
    setIsModalOpen(false);
    setModalImageSrc(null);
  };

  return (
    <>
      <MessageContainer isCurrentUser={isCurrentUser}>
        <MessageBubble isCurrentUser={isCurrentUser} status={message.sendStatus}>
          <MessageHeader>
            <MessageSender isCurrentUser={isCurrentUser}>{sender}</MessageSender>
            <MessageTime>{formattedTime}</MessageTime>
          </MessageHeader>
          
          {isEmoji ? (
            <EmojiContainer>
              {emojiUrl && !imageError ? (
                <EmojiImage 
                  src={emojiUrl} 
                  alt={emojiDesc || "Emoji"} 
                  onError={handleImageError}
                />
              ) : (
                <MessageEmoji>
                  <span role="img" aria-label={emojiDesc || "Emoji"}>🎭</span>
                  <EmojiName>{emojiDesc || "Emoji"}</EmojiName>
                </MessageEmoji>
              )}
            </EmojiContainer>
          ) : isImage ? (
            displayableImageSrc && !imageError ? ( 
              <ChatMessageImage 
                src={displayableImageSrc} 
                alt="Received Image" 
                onError={handleImageError}
                onClick={() => handleImageClick(displayableImageSrc)}
              />
            ) : (
              <ImagePlaceholder>
                <FiImage size={20} />
                {/* Show loading or error state based on flags */}
                <span>{isImageLoading ? 'Image (Loading...)' : 'Image (Data unavailable)'}</span>
              </ImagePlaceholder>
            )
          ) : (
            <MessageContent>{displayContent}</MessageContent>
          )}

          {isCurrentUser && message.sendStatus && (
            <StatusIndicator status={message.sendStatus}>{getStatusText()}</StatusIndicator>
          )}

          {isCurrentUser && message.sendStatus === 'failed' && (
            <ResendButton onClick={handleResend} title="Resend message">
              <FiRefreshCw size={12} />
            </ResendButton>
          )}
        </MessageBubble>
      </MessageContainer>

      {/* Image Modal */}
      {isModalOpen && modalImageSrc && (
        <ModalOverlay onClick={closeModal}> {/* Click background to close */}
          <ModalContent 
            src={modalImageSrc} 
            alt="Enlarged Image" 
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking the image itself
          />
        </ModalOverlay>
      )}
    </>
  );
};

export default Message;   