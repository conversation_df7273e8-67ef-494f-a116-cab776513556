import React, { useState } from 'react';
import styled from 'styled-components';
import { Fi<PERSON>, <PERSON><PERSON>ye, FiEyeOff } from 'react-icons/fi';
import { useApp } from '@/context/AppContext';
import { toast } from 'react-toastify';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
  position: relative;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: var(--text-color);
`;

const CloseButton = styled.button`
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: var(--text-color);
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: bold;
  color: var(--text-color);
`;

const Input = styled.input`
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: var(--accent-color);
  }
`;

const InputGroup = styled.div`
  display: flex;
  align-items: center;
`;

const ToggleButton = styled.button`
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: var(--text-color);
  }
`;

const HelpText = styled.p`
  font-size: 0.8rem;
  color: var(--text-secondary);
`;

const SaveButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 8px;
  
  &:hover {
    background-color: var(--secondary-color);
  }
`;

interface SettingsModalProps {
  onClose: () => void;
  passcode?: string;
  onPasscodeChange?: (passcode: string) => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ 
  onClose, 
  passcode = 'unlock',
  onPasscodeChange = () => {}
}) => {
  const { config, setConfig, stopPolling, startPolling } = useApp();
  
  const [showPasscode, setShowPasscode] = useState(false);
  
  const [formData, setFormData] = useState({
    serviceURL: config.serviceURL,
    apiKey: config.apiKey,
    pollInterval: config.pollInterval.toString(),
    username: config.username,
    passcode: passcode,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Stop current polling
    stopPolling();
    
    // Update config
    const newConfig = {
      ...config,
      serviceURL: formData.serviceURL,
      apiKey: formData.apiKey,
      pollInterval: parseInt(formData.pollInterval) || 1,
      username: formData.username,
    };
    
    setConfig(newConfig);
    
    // Restart polling with new config
    startPolling();
    
    if (onPasscodeChange && formData.passcode !== passcode) {
      onPasscodeChange(formData.passcode);
    }
    
    onClose();
    toast.success('Settings saved successfully!');
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Settings</ModalTitle>
          <CloseButton onClick={onClose}>
            <FiX />
          </CloseButton>
        </ModalHeader>
        
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="serviceURL">Service URL</Label>
            <Input
              type="text"
              id="serviceURL"
              name="serviceURL"
              value={formData.serviceURL}
              onChange={handleChange}
              placeholder="http://localhost:8080"
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="apiKey">API Key</Label>
            <Input
              type="password"
              id="apiKey"
              name="apiKey"
              value={formData.apiKey}
              onChange={handleChange}
              placeholder="Your API key"
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="pollInterval">Poll Interval (seconds)</Label>
            <Input
              type="number"
              id="pollInterval"
              name="pollInterval"
              value={formData.pollInterval}
              onChange={handleChange}
              min="1"
              max="60"
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="username">Your Display Name</Label>
            <Input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              placeholder="Your name"
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="passcode">Focus Protection Passcode</Label>
            <InputGroup>
              <Input
                type={showPasscode ? "text" : "password"}
                id="passcode"
                name="passcode"
                value={formData.passcode}
                onChange={handleChange}
                placeholder="Enter passcode"
                required
              />
              <ToggleButton 
                type="button" 
                onClick={() => setShowPasscode(!showPasscode)}
              >
                {showPasscode ? <FiEyeOff /> : <FiEye />}
              </ToggleButton>
            </InputGroup>
            <HelpText>
              This passcode will be required to unlock the screen when the window loses focus.
            </HelpText>
          </FormGroup>
          
          <SaveButton type="submit">Save Settings</SaveButton>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default SettingsModal; 