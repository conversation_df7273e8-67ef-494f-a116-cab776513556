import React, { useState } from 'react';
import styled from 'styled-components';
import { FiSearch, FiClock, FiTrash2 } from 'react-icons/fi';
import { useApp } from '@/context/AppContext';

const SidebarContainer = styled.div`
  width: 280px;
  background-color: var(--card-background);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const SidebarHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  font-weight: bold;
  font-size: 1.1rem;
`;

const SidebarContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
`;

const ActionGroup = styled.div`
  margin-bottom: 24px;
`;

const ActionTitle = styled.h3`
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const InputGroup = styled.div`
  display: flex;
  gap: 8px;
`;

const Input = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
`;

const Button = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--secondary-color);
  }
`;

const EmojiGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-top: 8px;
`;

const EmojiButton = styled.button`
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  color: black;

  &:hover {
    background-color: var(--accent-color);
    color: black;
  }
`;

const commonEmojis = [
  'mt', 'yt', 'miss', 'xqb', 
  'wt', 'jy'
];

const Sidebar: React.FC = () => {
  const { fetchHistory, searchMessages, sendEmoji, clearMessages } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [historyCount, setHistoryCount] = useState('10');
  const [customEmoji, setCustomEmoji] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      searchMessages(searchQuery);
    }
  };

  const handleFetchHistory = (e: React.FormEvent) => {
    e.preventDefault();
    const count = parseInt(historyCount);
    if (!isNaN(count) && count > 0) {
      fetchHistory(count);
    }
  };

  const handleSendEmoji = (emoji: string) => {
    sendEmoji(emoji);
  };
 
  const handleCustomEmoji = (e: React.FormEvent) => {
    e.preventDefault();
    if (customEmoji.trim()) {
      sendEmoji(customEmoji);
      setCustomEmoji('');
    }
  };

  const handleClearMessages = () => {
    if (window.confirm('Are you sure you want to clear all messages?')) {
      clearMessages();
    }
  };

  return (
    <SidebarContainer>
      <SidebarHeader>Actions</SidebarHeader>
      <SidebarContent>
        <ActionGroup>
          <ActionTitle>Search Messages</ActionTitle>
          <ActionForm onSubmit={handleSearch}>
            <InputGroup>
              <Input
                type="text"
                placeholder="Enter keyword..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button type="submit">
                <FiSearch />
              </Button>
            </InputGroup>
          </ActionForm>
        </ActionGroup>

        <ActionGroup>
          <ActionTitle>Fetch History</ActionTitle>
          <ActionForm onSubmit={handleFetchHistory}>
            <InputGroup>
              <Input
                type="number"
                placeholder="Number of messages"
                value={historyCount}
                onChange={(e) => setHistoryCount(e.target.value)}
                min="1"
              />
              <Button type="submit">
                <FiClock />
              </Button>
            </InputGroup>
          </ActionForm>
        </ActionGroup>

        <ActionGroup>
          <ActionTitle>Quick Emojis</ActionTitle>
          <EmojiGrid>
            {commonEmojis.map((emoji) => (
              <EmojiButton 
                key={emoji} 
                onClick={() => handleSendEmoji(emoji)}
              >
                {emoji}
              </EmojiButton>
            ))}
          </EmojiGrid>
          <ActionForm onSubmit={handleCustomEmoji} style={{ marginTop: '8px' }}>
            <InputGroup>
              <Input
                type="text"
                placeholder="Custom emoji name"
                value={customEmoji}
                onChange={(e) => setCustomEmoji(e.target.value)}
              />
              <Button type="submit">Send</Button>
            </InputGroup>
          </ActionForm>
        </ActionGroup>

        <ActionGroup>
          <ActionTitle>Clear Messages</ActionTitle>
          <Button onClick={handleClearMessages} style={{ backgroundColor: 'var(--error-color)' }}>
            <FiTrash2 /> Clear All
          </Button>
        </ActionGroup>
      </SidebarContent>
    </SidebarContainer>
  );
};

export default Sidebar; 