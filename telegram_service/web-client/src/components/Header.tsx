import React from 'react';
import styled from 'styled-components';
import { FiSettings, FiMenu, FiWifi, FiWifiOff } from 'react-icons/fi';

const HeaderContainer = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Logo = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  display: flex;
  align-items: center;
`;

const Controls = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const IconButton = styled.button`
  background: transparent;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const ConnectionStatus = styled.div<{ connected: boolean }>`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  color: ${props => props.connected ? '#4caf50' : '#f44336'};
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 12px;
`;

interface HeaderProps {
  onSettingsClick: () => void;
  onToggleSidebar: () => void;
  isConnected: boolean;
}

const Header: React.FC<HeaderProps> = ({ onSettingsClick, onToggleSidebar, isConnected }) => {
  return (
    <HeaderContainer>
      <Logo>
        <IconButton onClick={onToggleSidebar}>
          <FiMenu />
        </IconButton>
        Jay's Blog
      </Logo>
      
      <Controls>
        <ConnectionStatus connected={isConnected}>
          {isConnected ? <FiWifi /> : <FiWifiOff />}
          {isConnected ? 'Connected' : 'Disconnected'}
        </ConnectionStatus>
        
        <IconButton onClick={onSettingsClick}>
          <FiSettings />
        </IconButton>
      </Controls>
    </HeaderContainer>
  );
};

export default Header; 