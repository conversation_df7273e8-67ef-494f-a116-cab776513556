import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const ProtectionOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 1000;
  color: #333;
  padding: 20px;
  text-align: left;
  overflow-y: auto;
  
  /* Ensure it's visible even in small viewports */
  min-height: 100%;
  min-width: 100%;
  
  /* Handle mobile orientation changes */
  @media screen and (max-width: 768px) {
    padding: 16px;
  }
  
  @media screen and (max-width: 480px) {
    padding: 12px;
  }
`;

const BlogContainer = styled.div`
  width: 100%;
  max-width: 720px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 32px;
  margin-top: 20px;
  
  @media screen and (max-width: 768px) {
    padding: 24px;
  }
  
  @media screen and (max-width: 480px) {
    padding: 16px;
    border-radius: 12px;
  }
`;

const BlogTitle = styled.h1`
  font-size: 2rem;
  color: #333;
  margin-bottom: 16px;
`;

const BlogMeta = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 24px;
`;

const BlogContent = styled.div`
  font-size: 1.1rem;
  line-height: 1.8;
  color: #444;
  
  p {
    margin-bottom: 24px;
  }

  h2 {
    font-size: 1.5rem;
    color: #333;
    margin: 32px 0 16px;
  }

  blockquote {
    border-left: 4px solid #ddd;
    padding-left: 16px;
    margin: 24px 0;
    font-style: italic;
    color: #666;
  }
`;

const AppTitle = styled.div`
  font-size: 1.4rem;
  color: #333;
  font-weight: 600;
  
  @media screen and (max-width: 480px) {
    font-size: 1.2rem;
  }
`;

const KeyboardShortcut = styled.div`
  font-size: 0.7rem;
  color: #999;
  margin-top: 8px;
  
  @media screen and (max-width: 480px) {
    margin-top: 12px;
  }
`;

interface FocusProtectionProps {
  onUnlock: () => void;
  passcode: string;
}

const FocusProtection: React.FC<FocusProtectionProps> = ({ onUnlock }) => {
  const [clickPattern, setClickPattern] = useState<string[]>([]);

  const handleTitleClick = (word: string) => {
    const newPattern = [...clickPattern, word];
    setClickPattern(newPattern);

    const expectedPattern = ['Art', 'Focus', 'Art', 'Focus', 'Art', 'Focus'];
    if (newPattern.length > expectedPattern.length) {
      setClickPattern([word]);
      return;
    }

    for (let i = 0; i < newPattern.length; i++) {
      if (newPattern[i] !== expectedPattern[i]) {
        setClickPattern([word]);
        return;
      }
    }

    if (newPattern.length === expectedPattern.length) {
      onUnlock();
      setClickPattern([]);
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        onUnlock();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onUnlock]);

  // Add a visibility change handler specifically for mobile
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Force a re-render when visibility changes to ensure the lock screen is displayed
      if (document.visibilityState === 'visible') {
        // This is a trick to force a re-render
        setClickPattern(prev => [...prev]);
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Also handle page resize events to ensure proper display
    const handleResize = () => {
      // Force re-render on resize
      setClickPattern(prev => [...prev]);
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <ProtectionOverlay>
      <AppTitle>Daily Thoughts</AppTitle>
      <BlogContainer>
        <BlogTitle>
          <span onClick={() => handleTitleClick('Art')} style={{ cursor: 'pointer' }}>The Art</span>
          {' of '}
          <span onClick={() => handleTitleClick('Focus')} style={{ cursor: 'pointer' }}>Focus</span>
        </BlogTitle>
        <BlogMeta>Published on {new Date().toLocaleDateString()} · By Mind Cultivator</BlogMeta>
        <BlogContent>
          <p>
            In today's fast-paced digital world, maintaining focus has become increasingly challenging.
            The constant stream of notifications, emails, and social media updates creates an environment
            where deep work seems almost impossible. Yet, the ability to focus deeply is more valuable
            than ever before.
          </p>

          <h2>The Cost of Distraction</h2>
          <p>
            Research shows that it takes an average of 23 minutes to fully regain focus after a distraction.
            In a workday filled with interruptions, this can mean hours of lost productivity. Beyond mere
            productivity, constant task-switching and digital distraction can lead to increased stress,
            decreased creativity, and impaired decision-making abilities.
          </p>

          <blockquote>
            "The ability to perform deep work is becoming increasingly rare at exactly the same time it is becoming increasingly valuable in our economy." - Cal Newport
          </blockquote>

          <h2>Building Focus as a Skill</h2>
          <p>
            Focus, like any other skill, can be developed and strengthened through deliberate practice.
            Start with short periods of concentrated work—perhaps 25 minutes—and gradually extend these
            sessions as your "focus muscle" grows stronger. Create a dedicated workspace free from
            distractions, and establish clear boundaries between deep work and relaxation time.
          </p>

          <h2>The Role of Environment</h2>
          <p>
            Your physical and digital environment plays a crucial role in your ability to focus. Consider
            implementing these environmental changes:
          </p>
          <p>
            • Designate a specific area for focused work<br/>
            • Keep your workspace clean and organized<br/>
            • Use website blockers during deep work sessions<br/>
            • Turn off notifications on your devices<br/>
            • Use noise-canceling headphones or background white noise
          </p>

          <h2>The Power of Routine</h2>
          <p>
            Establishing a consistent routine helps train your brain to enter a focused state more easily.
            Many successful creators and knowledge workers have specific rituals they perform before
            beginning their deep work sessions. These routines serve as mental triggers, signaling to
            your brain that it's time to focus.
          </p>

          <p>
            Remember that focus isn't just about working more—it's about working better. When we work
            with full concentration, we not only produce higher quality output but also find more
            satisfaction in our work. In a world of endless distractions, the ability to focus deeply
            might just be the most important skill we can develop.
          </p>
        </BlogContent>
      </BlogContainer>
      <KeyboardShortcut>Press Ctrl+K to continue</KeyboardShortcut>
    </ProtectionOverlay>
  );
};

export default FocusProtection; 