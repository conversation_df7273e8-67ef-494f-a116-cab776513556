import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { useApp } from '@/context/AppContext';
import Message from './Message';

const MessageListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: 20px;
`;

const EmptyStateTitle = styled.h3`
  font-size: 1.2rem;
  margin-bottom: 8px;
`;

const EmptyStateText = styled.p`
  font-size: 0.9rem;
  max-width: 400px;
`;

interface MessageListProps {
  onLock?: () => void;
}

const MessageList: React.FC<MessageListProps> = ({ onLock }) => {
  const { messages } = useApp();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [lastTap, setLastTap] = useState<number>(0);
  
  // Double tap detection
  const handleTap = () => {
    const now = Date.now();
    const DOUBLE_TAP_DELAY = 300; // ms
    
    if (now - lastTap < DOUBLE_TAP_DELAY) {
      // Double tap detected
      if (onLock) {
        onLock();
      }
    }
    
    setLastTap(now);
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (messages.length === 0) {
    return (
      <MessageListContainer ref={containerRef} onClick={handleTap}>
        <EmptyState>
          <EmptyStateTitle>No Messages Yet</EmptyStateTitle>
          <EmptyStateText>
            Messages will appear here. You can fetch history or send a new message to get started.
          </EmptyStateText>
        </EmptyState>
      </MessageListContainer>
    );
  }

  return (
    <MessageListContainer ref={containerRef} onClick={handleTap}>
      {messages.map((message, index) => (
        <Message key={`${message.timestamp}-${index}`} message={message} />
      ))}
      <div ref={messagesEndRef} />
    </MessageListContainer>
  );
};

export default MessageList; 