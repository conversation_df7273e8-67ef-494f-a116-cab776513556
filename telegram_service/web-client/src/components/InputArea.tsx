import React, { useState, KeyboardEvent } from 'react';
import styled from 'styled-components';
import { FiSend } from 'react-icons/fi';
import { useApp } from '@/context/AppContext';

const InputContainer = styled.div`
  display: flex;
  padding: 16px;
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
`;

const InputForm = styled.form`
  display: flex;
  width: 100%;
  gap: 12px;
`;

const TextInput = styled.textarea`
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  min-height: 44px;
  max-height: 120px;
  
  &:focus {
    outline: none;
    border-color: var(--accent-color);
  }
`;

const SendButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--secondary-color);
  }
  
  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
`;

const InputArea: React.FC = () => {
  const { sendMessage, isConnected } = useApp();
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || !isConnected || isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      await sendMessage(message);
      setMessage('');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <InputContainer>
      <InputForm onSubmit={handleSubmit}>
        <TextInput
          placeholder="Type a message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={!isConnected}
        />
        <SendButton 
          type="submit" 
          disabled={!message.trim() || !isConnected || isSubmitting}
        >
          <FiSend />
        </SendButton>
      </InputForm>
    </InputContainer>
  );
};

export default InputArea; 