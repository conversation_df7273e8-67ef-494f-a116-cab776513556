:root {
  --primary-color: #2b5278;
  --secondary-color: #3b6a9b;
  --accent-color: #5b9bd5;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  --message-sent: #e3f2fd;
  --message-received: #ffffff;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --error: #f44336;
  --error-light: #ffebee;
  --warning: #ff9800;
  --success: #4caf50;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background: var(--primary-color);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:hover {
  background: var(--secondary-color);
}

button:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

input, textarea {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

input:focus, textarea:focus {
  border-color: var(--accent-color);
}

a {
  color: var(--accent-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.card {
  background: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 16px;
}

.emoji {
  font-size: 1.2em;
} 