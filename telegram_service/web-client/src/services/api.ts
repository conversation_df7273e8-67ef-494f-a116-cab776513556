import axios from 'axios';
import { ApiResponse, Command, Config, MessagesResponse } from '@/types';

const api = {
  async getMessages(config: Config): Promise<MessagesResponse> {
    try {
      const response = await axios.post(
        `${config.serviceURL}/api/get_messages`,
        {
          action: 'get_messages',
          api_key: config.apiKey,
          last_timestamp: config.lastTimestamp,
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      return { success: false, messages: [] };
    }
  },

  async sendCommand(config: Config, command: Command): Promise<ApiResponse<any>> {
    try {
      const response = await axios.post(
        `${config.serviceURL}/api/add_command`,
        {
          action: 'add_command',
          api_key: config.apiKey,
          command,
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error sending command:', error);
      return { success: false, error: 'Failed to send command' };
    }
  },
};

export default api; 