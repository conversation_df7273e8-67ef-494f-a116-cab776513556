import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import MessageList from './components/MessageList';
import InputArea from './components/InputArea';
import SettingsModal from './components/SettingsModal';
import FocusProtection from './components/FocusProtection';
import { useApp } from './context/AppContext';

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
`;

const MainContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
`;

// Default passcode for unlocking the screen
const DEFAULT_PASSCODE = '4608';

// Function to detect if the device is mobile
const isMobileDevice = () => {
  return window.innerWidth <= 768;
};

const App: React.FC = () => {
  const { isConnected } = useApp();
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [showSidebar, setShowSidebar] = useState<boolean>(!isMobileDevice());
  const [isLocked, setIsLocked] = useState<boolean>(true);
  const [passcode, setPasscode] = useState<string>(
    localStorage.getItem('focusProtectionPasscode') || DEFAULT_PASSCODE
  );
  
  // Handle window focus/blur events
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Lock immediately when document becomes hidden
        setIsLocked(true);
      }
    };
    
    const handleBlur = () => {
      // Lock immediately when window loses focus
      setIsLocked(true);
    };
    
    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleBlur);
    
    // Clean up
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);
  
  // Add keyboard shortcut to toggle lock state
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Toggle lock state with Ctrl+K
      if (e.key === 'k' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        setIsLocked(prevLocked => !prevLocked);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  
  // Save passcode to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('focusProtectionPasscode', passcode);
  }, [passcode]);

  // Handle window resize to adjust sidebar visibility
  useEffect(() => {
    const handleResize = () => {
      // Only auto-hide sidebar when it's currently visible
      if (showSidebar && isMobileDevice()) {
        setShowSidebar(false);
      }
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [showSidebar]);

  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };
  
  const handleUnlock = () => {
    setIsLocked(false);
  };
  
  const updatePasscode = (newPasscode: string) => {
    if (newPasscode && newPasscode.trim()) {
      setPasscode(newPasscode.trim());
    }
  };

  const handleLock = () => {
    setIsLocked(true);
  };

  return (
    <AppContainer>
      <Header 
        onSettingsClick={toggleSettings} 
        onToggleSidebar={toggleSidebar}
        isConnected={isConnected}
      />
      
      <MainContent>
        {showSidebar && <Sidebar />}
        
        <ChatContainer>
          <MessageList onLock={handleLock} />
          <InputArea />
        </ChatContainer>
      </MainContent>
      
      {showSettings && (
        <SettingsModal 
          onClose={toggleSettings} 
          passcode={passcode}
          onPasscodeChange={updatePasscode}
        />
      )}
      
      {isLocked && <FocusProtection onUnlock={handleUnlock} passcode={passcode} />}
    </AppContainer>
  );
};

export default App; 