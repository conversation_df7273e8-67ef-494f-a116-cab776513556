{"name": "telegram-web-client", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"axios": "^1.6.2", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-toastify": "^9.1.3", "styled-components": "^6.1.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.54.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.3.2", "vite": "^5.0.2"}}